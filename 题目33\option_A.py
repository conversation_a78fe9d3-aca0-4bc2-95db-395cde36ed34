# 选项A代码 - 对应迷宫A
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap

# 设置中文字体
plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

def create_maze():
    """创建25x25复杂迷宫"""
    maze = np.ones((25, 25))  # 1表示墙，0表示路径

    paths = [
        [(1,1), (1,2), (1,3), (1,4), (1,5), (1,6), (1,7), (1,8), (1,9), (1,10), (1,11), (1,12)],
        [(1,12), (2,12), (3,12), (4,12), (5,12), (6,12), (7,12), (8,12), (9,12), (10,12)],
        [(10,12), (10,13), (10,14), (10,15), (10,16), (10,17), (10,18), (10,19), (10,20), (10,21), (10,22), (10,23)],
        [(10,23), (11,23), (12,23), (13,23), (14,23), (15,23), (16,23), (17,23), (18,23), (19,23), (20,23), (21,23), (22,23), (23,23)],

        # 分支路径1 - 左上区域
        [(1,4), (2,4), (3,4), (4,4), (5,4), (6,4), (7,4), (8,4)],
        [(3,4), (3,5), (3,6), (3,7), (3,8), (3,9)],
        [(5,4), (5,5), (5,6), (5,7)],
        [(7,4), (7,5), (7,6), (7,7), (7,8)],

        # 分支路径2 - 中上区域
        [(4,12), (4,13), (4,14), (4,15), (4,16), (4,17)],
        [(6,12), (6,11), (6,10), (6,9), (6,8), (6,7), (6,6)],
        [(8,12), (8,11), (8,10), (8,9)],

        # 分支路径3 - 右侧区域
        [(10,16), (11,16), (12,16), (13,16), (14,16), (15,16)],
        [(12,16), (12,17), (12,18), (12,19), (12,20), (12,21)],
        [(14,16), (14,15), (14,14), (14,13), (14,12), (14,11)],

        # 分支路径4 - 下方区域
        [(15,23), (15,22), (15,21), (15,20), (15,19), (15,18)],
        [(17,23), (17,22), (17,21), (17,20)],
        [(19,23), (19,22), (19,21), (19,20), (19,19)],

        # 额外复杂路径
        [(1,8), (2,8), (3,8), (4,8), (5,8)],
        [(3,9), (4,9), (5,9), (6,9), (7,9), (8,9), (9,9)],
        [(9,9), (9,10), (9,11), (9,12)],
        [(14,11), (15,11), (16,11), (17,11), (18,11)],
        [(18,11), (18,12), (18,13), (18,14), (18,15)],

        # 死胡同
        [(3,6), (2,6), (2,5)],
        [(7,8), (8,8), (9,8), (9,7), (9,6)],
        [(12,21), (13,21), (13,20), (13,19)],
        [(18,15), (19,15), (20,15), (21,15)],
        [(15,18), (16,18), (17,18), (18,18)],
    ]

    # 设置路径
    for path in paths:
        for x, y in path:
            if 0 <= x < 25 and 0 <= y < 25:
                maze[x, y] = 0

    return maze

def visualize_maze():
    """可视化迷宫A"""
    maze = create_maze()

    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(10, 10))

    # 定义颜色：白色(路径)，黑色(墙)
    colors = ['white', 'black']
    cmap = ListedColormap(colors)

    # 绘制迷宫
    im = ax.imshow(maze, cmap=cmap, vmin=0, vmax=1)
    ax.set_title('迷宫A - 选项1生成结果', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks([])
    ax.set_yticks([])

    # 添加网格
    for x in range(26):
        ax.axhline(x-0.5, color='gray', linewidth=0.3, alpha=0.5)
        ax.axvline(x-0.5, color='gray', linewidth=0.3, alpha=0.5)

    # 标记起点和终点
    ax.plot(1, 1, 'go', markersize=10, label='起点(1,1)')
    ax.plot(23, 23, 'ro', markersize=10, label='终点(23,23)')
    ax.legend(loc='upper right', bbox_to_anchor=(0.98, 0.98))

    plt.tight_layout()
    plt.savefig('maze_A_option1.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    visualize_maze()
